// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useState } from "react";
import {
    Dimensions,
    type LayoutChangeEvent,
    type StyleProp,
    Text,
    View,
    type ViewStyle,
} from "react-native";

import Files from "@components/files";
import FormattedText from "@components/formatted_text";
import JumboEmoji from "@components/jumbo_emoji";
import { Screens } from "@constants";
import { THREAD } from "@constants/screens";
import { isEdited as postEdited, isPostFailed } from "@utils/post";
import { changeOpacity, makeStyleSheetFromTheme } from "@utils/theme";

import Acknowledgements from "./acknowledgements";
import AddMembers from "./add_members";
import Content from "./content";
import Failed from "./failed";
import Message from "./message";
import Reactions from "./reactions";

import type PostModel from "@typings/database/models/servers/post";
import type { SearchPattern } from "@typings/global/markdown";
import AudioFile from "@app/components/files/audio_file";

import Vedio from "./vedio/vedio";
import FormattedTime from "../../../../components/formatted_time";
import type { UserModel } from "@app/database/models/server";
import { getUserTimezone } from "@app/utils/user";
import { typography } from "@app/utils/typography";
import {
    detectTextDirection,
    getTextDirectionStyle,
    shouldUseRTLLayout,
} from "@utils/text_direction";
import ArabicNumbers from "@app/utils/englishNumberToArabic";
import CompassIcon from "@app/components/compass_icon";
import VideoMetadata from "@app/components/video_metadata";
import { useIntl } from "react-intl";
import MessageCornerSvg from "./message/message_corner_svg";
import { useServerUrl } from "@app/context/server";
import { useUserLocale } from "@app/context/user_locale";
// Removed unused imports
import MessageProfileHeader from "./message_profile_header/index";
import { withDatabase, withObservables } from "@nozbe/watermelondb/react";
import { observePostAuthor } from "@queries/servers/post";
import { observeFilesForPost } from "@queries/servers/file";
import { switchMap } from "rxjs/operators";
import { from as from$ } from "rxjs";
import type { WithDatabaseArgs } from "@typings/database/database";

type BodyProps = {
    replayPostOwner?: UserModel[] | undefined;
    replayPost?: PostModel[] | undefined;

    threadLength?: number | undefined;
    appsEnabled: boolean;
    hasFiles: boolean;
    hasReactions: boolean;
    highlight: boolean;
    highlightReplyBar: boolean;
    isCRTEnabled?: boolean;
    isEphemeral: boolean;
    isFirstReply?: boolean;
    isJumboEmoji: boolean;
    isLastReply?: boolean;
    isPendingOrFailed: boolean;
    isPostAcknowledgementEnabled?: boolean;
    isPostAddChannelMember: boolean;
    location: string;
    post: PostModel;
    searchPatterns?: SearchPattern[];
    showAddReaction?: boolean;
    theme: Theme;
    isCurrentUser: boolean;
    isInSearch?: boolean | undefined;
    currentUser?: UserModel | undefined;
    setLastViewedFileInfo?: (fileInfo: FileInfo) => void | undefined;
    channelType?: ChannelType | undefined;
    fromResult?: boolean | undefined;
    author?: UserModel; // Post author for voice message avatars
    files?: FileInfo[];
    showProfileImage?: boolean; // Whether to show profile image based on time-based grouping
};

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => {
    return {
        time: {},
        messageContainer: { width: "100%" },
        replyBar: {
            backgroundColor: theme.centerChannelColor,
            opacity: 0.1,
            marginLeft: 1,
            marginRight: 70,
            width: 3,
            flexBasis: 3,
        },
        replyBarFirst: { paddingTop: 10 },
        replyBarLast: { paddingBottom: 10 },
        replyMention: {
            backgroundColor: theme.mentionHighlightBg,
            opacity: 1,
        },
        message: {
            color: theme.centerChannelColor,
            fontSize: 15,
            lineHeight: 20,
        },
        messageContainerWithReplyBar: {
            flexDirection: "row",
            width: "100%",
        },
    };
});

const Body = ({
    replayPost = undefined,
    replayPostOwner = undefined,
    threadLength = 0,
    appsEnabled,
    hasFiles,
    hasReactions,
    highlight,
    highlightReplyBar,
    isCRTEnabled,
    isEphemeral,
    isFirstReply,
    isJumboEmoji,
    isLastReply,
    isPendingOrFailed,
    isPostAcknowledgementEnabled,
    isPostAddChannelMember,
    location,
    post,
    searchPatterns,
    showAddReaction,
    theme,
    isCurrentUser = false,
    setLastViewedFileInfo = undefined,
    currentUser = undefined,
    isInSearch = undefined,
    channelType = undefined,
    fromResult,
    author,
    files,
    showProfileImage = false,
}: BodyProps) => {
    const userLocale = useUserLocale();

    const textDirection = detectTextDirection(post.message);
    const isRTL = shouldUseRTLLayout(post.message, userLocale);

    const textDirectionStyle = getTextDirectionStyle(
        post.message,
        Boolean(isCurrentUser),
        userLocale
    );

    const intl = useIntl();

    const style = getStyleSheet(theme);
    const isEdited = postEdited(post);
    const isFailed = isPostFailed(post);
    const [layoutWidth, setLayoutWidth] = useState(0);
    const hasBeenDeleted = Boolean(post.deleteAt);
    const windowWidth = Dimensions.get("window").width;

    let body;
    let message;
    let vedio_call_component;
    let voice;
    let acknowledgements;

    let messageTime;

    let messageTimeAudio;

    const isReplyPost = Boolean(
        post.rootId && (!isEphemeral || !hasBeenDeleted) && location !== THREAD
    );
    const hasContent = Boolean(
        post.metadata?.embeds?.length ||
            (appsEnabled && post.props?.app_bindings?.length) ||
            post.props?.attachments?.length
    );

    const replyBarStyle = useCallback((): StyleProp<ViewStyle> | undefined => {
        if (!isReplyPost || (isCRTEnabled && location === Screens.PERMALINK)) {
            return undefined;
        }

        const barStyle: StyleProp<ViewStyle> = [style.replyBar];

        if (isFirstReply) {
            barStyle.push(style.replyBarFirst);
        }

        if (isLastReply) {
            barStyle.push(style.replyBarLast);
        }

        if (highlightReplyBar) {
            barStyle.push(style.replyMention);
        }

        return barStyle;
    }, []);

    const onLayout = useCallback(
        (e: LayoutChangeEvent) => {
            if (location === Screens.SAVED_MESSAGES) {
                setLayoutWidth(e.nativeEvent.layout.width);
            }
        },
        [location]
    );

    const acknowledgementsVisible =
        post.props?.ack === true ||
        (isPostAcknowledgementEnabled &&
            post.metadata?.priority?.requested_ack);
    const reactionsVisible = hasReactions && showAddReaction;
    const [isHasReplay] = useState<Boolean>(
        post.props?.replay_post_id?.length > 0 || false
    );

    messageTime = currentUser && (
        <FormattedTime
            timezone={getUserTimezone(currentUser)}
            isMilitaryTime={false}
            value={post.createAt}
            style={{
                color: isCurrentUser
                    ? "white"
                    : changeOpacity(theme.sidebarText, 0.5),
                ...typography("Body", 75, "Regular"),
                flexDirection: "row",
                alignItems: "center",
            }}
            testID="post_header.date_time"
        />
    );
    const serverUrl = useServerUrl();

    const [messageWidth, changeWidth] = useState(0);
    const defaultWidth = windowWidth < 400 ? 290 : 306;

    const [isVedieUrl] = useState(
        (post?.props !== undefined &&
            post?.props["meeting_link"] !== undefined &&
            post?.props["meeting_link"] != null) ||
            post?.message?.includes("https://meet") ||
            post?.message?.includes("https://meet.meta-yemen.one")
    );

    if (hasBeenDeleted) {
        body = (
            <View
                style={{
                    maxWidth: (windowWidth / 4) * 3,
                    backgroundColor: isCurrentUser
                        ? theme.buttonBg
                        : theme.sidebarTextHoverBg,
                    borderRadius: 15,
                    overflow: "hidden",
                }}
            >
                {/* No profile header for deleted messages */}
                <View style={{ paddingHorizontal: 12, paddingBottom: 8 }}>
                    <FormattedText
                        style={[
                            style.message,
                            { fontFamily: "IBMPlexSansArabic-Bold" },
                        ]}
                        id="post_body.deleted"
                        defaultMessage="(message deleted)"
                    />
                </View>
            </View>
        );
    } else if (
        (post?.props !== undefined &&
            post?.props["meeting_link"] !== undefined &&
            post?.props["meeting_link"] != null) ||
        post?.message?.includes("https://meet") ||
        post?.message?.includes("https://meet.meta-yemen.one")
    ) {
        vedio_call_component = (
            <View
                style={{
                    width: defaultWidth,
                    backgroundColor: isCurrentUser
                        ? theme.buttonBg
                        : theme.sidebarTextHoverBg,
                    borderRadius: 15,
                    overflow: "hidden",
                }}
            >
                {/* No profile header for video calls */}
                <Vedio
                    width={defaultWidth}
                    isCurrentUser={post.userId === currentUser?.id}
                    post={post}
                    theme={theme}
                />
            </View>
        );
    } else if (post.type === "custom_voice") {
        voice = (
            <View
                style={{
                    width: defaultWidth,
                    backgroundColor: isCurrentUser
                        ? theme.buttonBg
                        : theme.sidebarTextHoverBg,
                    borderRadius: 15,
                    overflow: "hidden",
                }}
            >
                {/* No profile header needed - avatar shown at left edge for voice messages */}
                <View style={{ paddingHorizontal: 12, paddingBottom: 8 }}>
                    <AudioFile
                        isCurrentUser={isCurrentUser}
                        // acknolowgment={acknowledgementsAudio}
                        file={{
                            id: post.props["fileId"],
                            bytesRead: 0,
                            channel_id: post.channelId,
                            clientId: "",
                            create_at: post.createAt,
                            delete_at: post.deleteAt,
                            extension: "",
                            failed: undefined,
                            has_preview_image: false,
                            height: 0,
                            localPath: undefined,
                            mime_type: "audio/m4a",
                            mini_preview: undefined,
                            name: "",
                            post_id: undefined,
                            size: 0,
                            update_at: post.updateAt,
                            uri: undefined,
                            user_id: "",
                            width: 0,
                            postProps: {
                                duration: post.props["duration"],
                            },
                        }}
                        author={author}
                        post={post}
                        currentUser={currentUser}
                        acknowledgementsVisible={acknowledgementsVisible}
                        hasReactions={hasReactions}
                        location={location}
                        showTimestamp={true}
                        showReadReceipts={true}
                    />
                </View>
            </View>
        );
    } else if (isPostAddChannelMember) {
        message = (
            <View
                style={{
                    backgroundColor: isCurrentUser
                        ? theme.buttonBg
                        : theme.sidebarTextHoverBg,
                    borderRadius: 15,
                    overflow: "hidden",
                }}
            >
                {/* No profile header for add members messages */}
                <View style={{ paddingHorizontal: 12, paddingBottom: 8 }}>
                    <AddMembers location={location} post={post} theme={theme} />
                </View>
            </View>
        );
    } else if (isJumboEmoji) {
        message = (
            <View
                style={{
                    zIndex: 10,
                    minWidth: 20,
                    maxWidth: (windowWidth / 4) * 3,
                    flexWrap: "nowrap",
                    backgroundColor: isCurrentUser
                        ? theme.buttonBg
                        : theme.sidebarTextHoverBg,
                    borderRadius: 15,
                    overflow: "hidden",
                }}
            >
                {/* No profile header for jumbo emoji messages */}
                <View
                    style={{
                        paddingHorizontal: 0,
                        paddingTop: 2,
                        paddingBottom: 4,
                    }}
                >
                    <JumboEmoji
                        baseTextStyle={{ fontSize: 25, lineHeight: 40 }}
                        // isEdited={isEdited}
                        value={post.message}
                    />
                </View>
            </View>
        );
    } else if (post.message.length) {
        message = (
            <View>
                <Message
                    highlight={highlight}
                    isEdited={isEdited}
                    //isEdited={false}
                    isPendingOrFailed={isPendingOrFailed}
                    isReplyPost={isReplyPost}
                    layoutWidth={layoutWidth}
                    location={location}
                    post={post}
                    searchPatterns={searchPatterns}
                    theme={theme}
                    isCurrentUser={isCurrentUser}
                />
            </View>
        );
    }
    if (!hasBeenDeleted) {
        body = (
            <View
                style={{
                    // backgroundColor: "red",
                    // minWidth: 90,
                    // maxWidth: defaultWidth,
                    marginRight: isCurrentUser ? 0 : -60,
                    marginLeft: !isCurrentUser ? 0 : -60,
                }}
            >
                <View
                    style={
                        {
                            // backgroundColor: "black"
                            // minWidth: 90,
                            // maxWidth: defaultWidth,
                        }
                    }
                >
                    {/* WhatsApp-style message container */}
                    <View
                        style={{
                            flexDirection: "row",
                            alignItems: "flex-end", // Keep flex-end for proper bubble alignment
                            width: "100%",
                            justifyContent: isCurrentUser
                                ? "flex-start"
                                : "flex-end",
                        }}
                    >
                        {/* Profile image for other users - right side positioning */}
                        {((showProfileImage && !isCurrentUser) ||
                            (post.type === "custom_voice" &&
                                !isCurrentUser)) && (
                            <View
                                style={{
                                    position: "absolute",
                                    right: 12, // Proper spacing from right edge
                                    top: 0, // Align with top of message bubble
                                    zIndex: 1,
                                }}
                            >
                                <MessageProfileHeader
                                    post={post}
                                    isCurrentUser={isCurrentUser}
                                    size={
                                        post.type === "custom_voice" ? 38 : 32
                                    } // Larger avatar for voice messages
                                />
                            </View>
                        )}

                        {/* Profile image for current user - left side positioning */}
                        {((showProfileImage && isCurrentUser) ||
                            (post.type === "custom_voice" &&
                                isCurrentUser)) && (
                            <View
                                style={{
                                    position: "absolute",
                                    left: 12, // Proper spacing from left edge
                                    top: 0, // Align with top of message bubble
                                    zIndex: 1,
                                }}
                            >
                                <MessageProfileHeader
                                    post={post}
                                    isCurrentUser={isCurrentUser}
                                    size={
                                        post.type === "custom_voice" ? 38 : 32
                                    } // Larger avatar for voice messages
                                />
                            </View>
                        )}

                        <View
                            style={{
                                backgroundColor: isCurrentUser
                                    ? theme.buttonBg
                                    : theme.sidebarTextHoverBg,
                                minWidth: "auto", // Allow natural sizing
                                maxWidth: defaultWidth,
                                flexDirection: "column",
                                borderRadius: 12,
                                overflow: "hidden",
                                alignSelf: isCurrentUser
                                    ? "flex-end"
                                    : "flex-start", // Proper alignment
                                flexShrink: 1,
                                // Add margins to create space for avatars when they're shown
                                marginLeft:
                                    isCurrentUser &&
                                    (showProfileImage ||
                                        post.type === "custom_voice")
                                        ? 50
                                        : 0,
                                marginRight:
                                    !isCurrentUser &&
                                    (showProfileImage ||
                                        post.type === "custom_voice")
                                        ? 50
                                        : 0,
                            }}
                        >
                            <View>
                                {message && (
                                    <View
                                        style={{
                                            zIndex: -4,
                                            marginTop: 0, // Changed from "auto" to prevent emoji overflow
                                            transform: [
                                                {
                                                    scaleX: isCurrentUser
                                                        ? 1
                                                        : -1,
                                                },
                                            ],
                                            //marginRight: isCurrentUser ? -8 : 0, marginLeft: !isCurrentUser ? -8 : 0
                                        }}
                                    ></View>
                                )}
                                {
                                    <View>
                                        {
                                            //hasFiles &&
                                            <View
                                                style={{
                                                    borderTopLeftRadius: 10,
                                                    borderTopRightRadius: 10,
                                                    //top: 5,
                                                    marginBottom: 0,
                                                    //width: defaultWidth
                                                }}
                                            >
                                                <Files
                                                    isHasMessage={
                                                        post.message.length > 0
                                                    }
                                                    setLastViewedFileInfo={
                                                        setLastViewedFileInfo
                                                    }
                                                    failed={isFailed}
                                                    layoutWidth={defaultWidth}
                                                    location={location}
                                                    post={post}
                                                    isReplyPost={isReplyPost}
                                                    isCurrentUser={
                                                        isCurrentUser
                                                    }
                                                    author={author}
                                                    showMetadataOverlay={true}
                                                    metadataOverlayContent={
                                                        <View
                                                            style={{
                                                                flexDirection:
                                                                    "row",
                                                                alignItems:
                                                                    "center",
                                                                justifyContent:
                                                                    "flex-end",
                                                            }}
                                                        >
                                                            {messageTime}
                                                            {acknowledgementsVisible && (
                                                                <Acknowledgements
                                                                    iconColor="white"
                                                                    hasReactions={
                                                                        hasReactions
                                                                    }
                                                                    location={
                                                                        location
                                                                    }
                                                                    post={post}
                                                                    theme={
                                                                        theme
                                                                    }
                                                                    iconSize={
                                                                        16
                                                                    }
                                                                />
                                                            )}
                                                        </View>
                                                    }
                                                    files={files}
                                                    currentUser={currentUser}
                                                    acknowledgementsVisible={
                                                        acknowledgementsVisible
                                                    }
                                                    hasReactions={hasReactions}
                                                />
                                            </View>
                                        }

                                        {replayPost && (
                                            <View>
                                                <View
                                                    style={{
                                                        height: 50,
                                                        minWidth: 90,
                                                        maxWidth: defaultWidth,
                                                        borderTopLeftRadius: 15,
                                                        borderTopRightRadius: 15,
                                                    }}
                                                >
                                                    {replayPostOwner && (
                                                        <Text
                                                            style={{
                                                                ...typography(
                                                                    "Heading",
                                                                    200,
                                                                    "Light"
                                                                ),
                                                                color: theme.sidebarText,
                                                                marginStart: 15,
                                                                textAlign:
                                                                    "left",
                                                            }}
                                                        >
                                                            {replayPostOwner[0]
                                                                .firstName +
                                                                " " +
                                                                replayPostOwner[0]
                                                                    .lastName}
                                                        </Text>
                                                    )}
                                                    <Text
                                                        style={{
                                                            ...typography(
                                                                "Heading",
                                                                200,
                                                                "Light"
                                                            ),
                                                            color: isCurrentUser
                                                                ? theme.centerChannelBg
                                                                : changeOpacity(
                                                                      theme.sidebarText,
                                                                      0.16
                                                                  ),
                                                            marginStart: 15,
                                                            textAlign: "left",
                                                        }}
                                                    >
                                                        {replayPost[0].message}
                                                    </Text>
                                                </View>
                                            </View>
                                        )}

                                        <View
                                            style={{
                                                width: "100%", // Take full width
                                                paddingHorizontal: 8,
                                                paddingVertical: 3, // Add vertical padding to prevent emoji overflow
                                                direction: textDirection,
                                                // Apply 33px height for single-line text messages
                                                minHeight:
                                                    message &&
                                                    post.message &&
                                                    !hasFiles &&
                                                    !hasContent &&
                                                    !replayPost &&
                                                    post.message.length < 50 && // Rough estimate for single line
                                                    !post.message.includes(
                                                        "\n"
                                                    ) && // No line breaks
                                                    !post.message.includes(
                                                        "http"
                                                    ) && // No links
                                                    !post.message.includes(
                                                        "@"
                                                    ) && // No mentions
                                                    !post.message.includes("#") // No hashtags
                                                        ? 30
                                                        : undefined,
                                                ...textDirectionStyle,
                                            }}
                                        >
                                            {message}
                                        </View>

                                        <View
                                            style={{
                                                display: "flex",
                                                flexDirection: !isEdited
                                                    ? "row"
                                                    : "column",
                                                flexWrap: "wrap",
                                                justifyContent: "flex-end",
                                                paddingHorizontal: 30,
                                                alignSelf: isCurrentUser
                                                    ? "flex-end"
                                                    : "flex-start", // Align based on user
                                                minWidth: 50,
                                                maxWidth: defaultWidth,
                                                columnGap: 10,
                                            }}
                                        >
                                            {hasContent && !isVedieUrl && (
                                                <View>
                                                    <Content
                                                        isReplyPost={
                                                            isReplyPost
                                                        }
                                                        layoutWidth={
                                                            defaultWidth - 20
                                                        }
                                                        location={location}
                                                        post={post}
                                                        theme={theme}
                                                    />
                                                </View>
                                            )}
                                        </View>
                                    </View>
                                }

                                {vedio_call_component !== undefined &&
                                    vedio_call_component}
                                {voice !== undefined && voice}
                                {/* {hasContent&&conten} */}
                            </View>

                            <View
                                style={{
                                    width: isEdited ? "auto" : 90,
                                    flexDirection: "row",
                                    marginStart: isCurrentUser ? 15 : undefined,
                                    marginEnd: isCurrentUser ? undefined : 5,
                                    marginTop: 0,
                                    alignSelf: "flex-end",
                                    justifyContent: "space-between",
                                }}
                            >
                                {isEdited && (
                                    <Text
                                        style={{
                                            color: isCurrentUser
                                                ? "white"
                                                : theme.sidebarText,
                                            ...typography(
                                                "Body",
                                                75,
                                                "Regular"
                                            ),
                                        }}
                                    >
                                        {"(تم تعديل هذه الرسالة)"}
                                    </Text>
                                )}
                                {/* Metadata moved to overlays on media content - only show for non-media messages */}
                                {(!files || files.length === 0) && (
                                    <View
                                        style={{
                                            flexDirection: "row",
                                            alignItems: "center",
                                            marginStart: 30,
                                        }}
                                    >
                                        {messageTime}
                                        {acknowledgementsVisible && (
                                            <Acknowledgements
                                                iconColor={
                                                    isCurrentUser
                                                        ? "white"
                                                        : theme.sidebarText
                                                }
                                                hasReactions={hasReactions}
                                                location={location}
                                                post={post}
                                                theme={theme}
                                                iconSize={16}
                                            />
                                        )}
                                    </View>
                                )}
                            </View>
                        </View>

                        <View
                            style={{
                                position: "absolute",
                                bottom: -1,
                                start: isCurrentUser ? -6 : undefined,
                                end: !isCurrentUser ? -6 : undefined,
                            }}
                        >
                            {(!hasContent || post.type === "custom_voice") && (
                                <MessageCornerSvg
                                    isCurrentUser={isCurrentUser}
                                    color={
                                        isCurrentUser
                                            ? theme.buttonBg
                                            : theme.sidebarTextHoverBg
                                    }
                                />
                            )}
                        </View>
                    </View>

                    <View
                        style={{
                            flexDirection: "row-reverse",
                            justifyContent: !isCurrentUser
                                ? "flex-end"
                                : "flex-start",
                            marginTop: 5,
                        }}
                    >
                        {threadLength > 0 && channelType !== undefined && (
                            <View
                                style={{ flexDirection: "row", marginEnd: 6 }}
                            >
                                <View>
                                    <Text
                                        style={{
                                            color: changeOpacity(
                                                theme.centerChannelColor,
                                                0.64
                                            ),
                                            marginHorizontal: 4,
                                            ...typography(
                                                "Heading",
                                                200,
                                                "Light"
                                            ),
                                        }}
                                    >
                                        {ArabicNumbers(threadLength)}{" "}
                                        {intl.formatMessage({
                                            id: "repliesCount",
                                            defaultMessage: "repliesCount",
                                        })}
                                    </Text>
                                </View>
                                <CompassIcon
                                    color={changeOpacity(
                                        theme.centerChannelColor,
                                        0.64
                                    )}
                                    name="reply-outline"
                                    size={24}
                                />
                            </View>
                        )}

                        {reactionsVisible && (
                            <Reactions
                                location={location}
                                post={post}
                                theme={theme}
                            />
                        )}
                    </View>
                </View>
            </View>
        );
    }

    return (
        <View
            //style={style.messageContainerWithReplyBar}
            onLayout={onLayout}
            style={{ marginTop: 5 }}
        >
            <View style={replyBarStyle()} />
            {body}
            {isFailed && <Failed post={post} theme={theme} />}
        </View>
    );
};

const withBodyObservables = withObservables(
    ["post"],
    ({ database, post }: { post: PostModel } & WithDatabaseArgs) => {
        const author = observePostAuthor(database, post);
        const files = observeFilesForPost(database, post.id).pipe(
            switchMap((fileModels) =>
                from$(
                    Promise.all(
                        fileModels.map((f) => f.toFileInfo(post.userId))
                    )
                )
            )
        );

        return {
            author,
            files,
        };
    }
);

export default withDatabase(withBodyObservables(Body));
